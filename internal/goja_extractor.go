package internal

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"web_scanner/core"

	"github.com/dop251/goja"
)

// GojaExtractor 基于goja JavaScript引擎的敏感信息提取器
// 完全复用FindSomething项目的原版JavaScript检测逻辑，确保100%兼容性
type GojaExtractor struct {
	logger       *core.Logger
	errorManager *core.ErrorManager
	
	// JavaScript运行时和相关资源
	vm           *goja.Runtime
	extractFunc  goja.Callable
	mu           sync.Mutex // 保护JavaScript运行时的并发访问
	
	// 初始化状态
	initialized  bool
}

// NewGojaExtractor 创建新的goja敏感信息提取器
func NewGojaExtractor() *GojaExtractor {
	extractor := &GojaExtractor{
		logger:       core.GetLogger(),
		errorManager: core.GetErrorManager(),
		initialized:  false,
	}
	
	// 初始化JavaScript运行时
	if err := extractor.initializeJSRuntime(); err != nil {
		extractor.logger.Error("goja_extractor", "JavaScript运行时初始化失败", map[string]interface{}{
			"error": err.Error(),
		})
		return nil
	}
	
	extractor.logger.Info("goja_extractor", "goja敏感信息提取器初始化成功", nil)
	return extractor
}

// initializeJSRuntime 初始化JavaScript运行时环境
func (ge *GojaExtractor) initializeJSRuntime() error {
	ge.mu.Lock()
	defer ge.mu.Unlock()
	
	// 创建新的JavaScript运行时
	ge.vm = goja.New()
	
	// 直接使用内联的FindSomething JavaScript代码
	jsCode := ge.getFindSomethingJSCode()

	// 加载JavaScript代码到运行时
	_, err := ge.vm.RunString(jsCode)
	if err != nil {
		return fmt.Errorf("加载FindSomething JavaScript代码失败: %w", err)
	}
	
	// 获取extract_info函数
	extractInfoValue := ge.vm.Get("extract_info")
	if extractInfoValue == nil {
		return fmt.Errorf("未找到extract_info函数")
	}
	
	// 转换为可调用函数
	var ok bool
	ge.extractFunc, ok = goja.AssertFunction(extractInfoValue)
	if !ok {
		return fmt.Errorf("extract_info不是一个有效的函数")
	}
	
	ge.initialized = true
	ge.logger.Debug("goja_extractor", "JavaScript运行时初始化完成", map[string]interface{}{
		"vm_initialized": true,
		"extract_func_ready": true,
	})
	
	return nil
}

// getFindSomethingJSCode 获取FindSomething的核心JavaScript代码
func (ge *GojaExtractor) getFindSomethingJSCode() string {
	return `
// nuclei正则表达式数组 - 用于密钥检测
var nuclei_regex = [
    /["']?zopim[_-]?account[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?zhuliang[_-]?gh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?zensonatypepassword["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?zendesk[_-]?travis[_-]?github["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?yt[_-]?server[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?yt[_-]?partner[_-]?refresh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?yt[_-]?partner[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?yt[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?yt[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?yt[_-]?account[_-]?refresh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?yt[_-]?account[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?yangshun[_-]?gh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?yangshun[_-]?gh[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?www[_-]?googleapis[_-]?com["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?wpt[_-]?ssh[_-]?private[_-]?key[_-]?base64["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?wpt[_-]?ssh[_-]?connect["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?wpt[_-]?report[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?wpt[_-]?prepare[_-]?dir["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?wpt[_-]?db[_-]?user["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?wpt[_-]?db[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?wporg[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?wpjm[_-]?phpunit[_-]?google[_-]?geocode[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?wordpress[_-]?db[_-]?user["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?wordpress[_-]?db[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?wincert[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?widget[_-]?test[_-]?server["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?widget[_-]?fb[_-]?password[_-]?3["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?widget[_-]?fb[_-]?password[_-]?2["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?widget[_-]?fb[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?widget[_-]?basic[_-]?password[_-]?5["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?widget[_-]?basic[_-]?password[_-]?4["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?widget[_-]?basic[_-]?password[_-]?3["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?widget[_-]?basic[_-]?password[_-]?2["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?widget[_-]?basic[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?watson[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?watson[_-]?device[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?watson[_-]?conversation[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?wakatime[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?vscetoken["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?visual[_-]?recognition[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?virustotal[_-]?apikey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?vip[_-]?github[_-]?deploy[_-]?key[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?vip[_-]?github[_-]?deploy[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?vip[_-]?github[_-]?build[_-]?repo[_-]?deploy[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?v[_-]?sfdc[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?v[_-]?sfdc[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?usertravis["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?user[_-]?assets[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?user[_-]?assets[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?use[_-]?ssh["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?us[_-]?east[_-]?1[_-]?elb[_-]?amazonaws[_-]?com["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?urban[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?urban[_-]?master[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?urban[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?unity[_-]?serial["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,
    /["']?unity[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi
];

// 辅助函数：数组合并去重
function add(arr1, arr2) {
    if (!arr1) arr1 = [];
    if (!arr2) return arr1;

    var result = arr1.slice(); // 复制arr1
    for (var i = 0; i < arr2.length; i++) {
        if (result.indexOf(arr2[i]) === -1) {
            result.push(arr2[i]);
        }
    }
    return result;
}

// 密钥提取函数 - 使用nuclei正则表达式
function get_secret(data) {
    var result = [];
    for (var i = nuclei_regex.length - 1; i >= 0; i--) {
        var tmp_result = data.match(nuclei_regex[i]);
        if (tmp_result != null) {
            for (var j in tmp_result) {
                result.push(tmp_result[j]);
            }
        }
    }
    return result;
}

// 主要的敏感信息提取函数 - 完全基于FindSomething的extract_info函数
function extract_info(data) {
    var extract_data = {};

    // 提取身份证号码
    extract_data['sfz'] = data.match(/['"]((\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(\d{6}(18|19|20)\d{2}(0[1-9]|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)))['"]/g);

    // 提取手机号码
    extract_data['mobile'] = data.match(/['"](1(3([0-35-9]\d|4[1-8])|4[14-9]\d|5([\d]\d|7[1-79])|66\d|7[2-35-8]\d|8\d{2}|9[89]\d)\d{7})['"]/g);

    // 提取邮箱地址
    extract_data['mail'] = data.match(/['"][a-zA-Z0-9\._\-]*@[a-zA-Z0-9\._\-]{1,63}\.((?!js|css|jpg|jpeg|png|ico)[a-zA-Z]{2,})['"]/g);

    // 提取IP地址
    extract_data['ip'] = data.match(/['"](([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(\/.*?)?['"]/g);

    // 提取IP:端口
    extract_data['ip_port'] = data.match(/['"](([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\:\d{1,5}(\/.*?)?['"]/g);

    // 提取域名
    extract_data['domain'] = data.match(/['"](([a-zA-Z0-9]+:)?\/\/)?[a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw|我爱你|中国|公司|网络|在线|网址|网店|集团|中文网)(\:\d{1,5})?(\/)?['"]/g);

    // 提取路径
    extract_data['path'] = data.match(/['"](?:\/|\.\.\/|\.\/)[^\/\>\< \)\(\{\}\,\'\"\\]([^\>\< \)\(\{\}\,\'\"\\])*?['"]/g);

    // 提取不完整路径
    extract_data['incomplete_path'] = data.match(/['"][^\/\>\< \)\(\{\}\,\'\"\\][\w\/]*?\/[\w\/]*?['"]/g);

    // 提取URL
    extract_data['url'] = data.match(/['"](([a-zA-Z0-9]+:)?\/\/)?[a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw|我爱你|中国|公司|网络|在线|网址|网店|集团|中文网)(\:\d{1,5})?(\/.*?)?['"]/g);

    // 提取JWT令牌
    extract_data['jwt'] = data.match(/['"](ey[A-Za-z0-9_-]{10,}\.[A-Za-z0-9._-]{10,}|ey[A-Za-z0-9_\/+-]{10,}\.[A-Za-z0-9._\/+-]{10,})['"]/g);

    // 提取算法
    extract_data['algorithm'] = data.match(/\W(Base64\.encode|Base64\.decode|btoa|atob|CryptoJS\.AES|CryptoJS\.DES|JSEncrypt|rsa|KJUR|$\.md5|md5|sha1|sha256|sha512)[\(\.]/gi);

    // 提取密钥信息
    extract_data['secret'] = get_secret(data);

    // 从URL中进一步提取IP和域名信息
    if (extract_data['url']) {
        extract_data['url'].map(function(url) {
            extract_data['ip'] = add(extract_data['ip'], url.match(/['"](([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/g));
            extract_data['ip_port'] = add(extract_data['ip_port'], url.match(/['"](([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\:\d{1,5}(\/.*?)?['"]/g));
            extract_data['domain'] = add(extract_data['domain'], url.match(/['"](([a-zA-Z0-9]+:)?\/\/)?[a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw|我爱你|中国|公司|网络|在线|网址|网店|集团|中文网)(\:\d{1,5})?/g));
        });
    }

    return extract_data;
}
`
}

// ExtractSensitiveInfo 使用JavaScript引擎提取敏感信息
// 参数：
//   - data: 要分析的文本内容
//   - source: 信息来源（URL或文件路径）
//
// 返回值：
//   - *SensitiveInfo: 提取到的敏感信息结构体
func (ge *GojaExtractor) ExtractSensitiveInfo(data string, source string) *SensitiveInfo {
	if !ge.initialized {
		ge.logger.Error("goja_extractor", "提取器未初始化", map[string]interface{}{
			"source": source,
		})
		return nil
	}
	
	ge.logger.Debug("goja_extractor", "开始提取敏感信息", map[string]interface{}{
		"source":      source,
		"data_length": len(data),
	})
	
	// 使用互斥锁保护JavaScript运行时的并发访问
	ge.mu.Lock()
	defer ge.mu.Unlock()
	
	// 调用JavaScript的extract_info函数
	result, err := ge.extractFunc(goja.Undefined(), ge.vm.ToValue(data))
	if err != nil {
		ge.logger.Error("goja_extractor", "JavaScript函数执行失败", map[string]interface{}{
			"source": source,
			"error":  err.Error(),
		})
		return nil
	}
	
	// 将JavaScript结果转换为Go结构体
	sensitiveInfo := ge.convertJSResultToGoStruct(result, source)
	
	if sensitiveInfo != nil {
		ge.logger.Debug("goja_extractor", "敏感信息提取完成", map[string]interface{}{
			"source":     source,
			"sfz_count":  len(sensitiveInfo.SFZ),
			"phone_count": len(sensitiveInfo.Phone),
			"mail_count": len(sensitiveInfo.Mail),
			"ip_count":   len(sensitiveInfo.IP),
			"domain_count": len(sensitiveInfo.Domain),
			"secret_count": len(sensitiveInfo.Secret),
		})
	}
	
	return sensitiveInfo
}

// convertJSResultToGoStruct 将JavaScript结果转换为Go结构体
func (ge *GojaExtractor) convertJSResultToGoStruct(jsResult goja.Value, source string) *SensitiveInfo {
	// 将JavaScript对象转换为JSON字符串
	jsonStr, err := json.Marshal(jsResult.Export())
	if err != nil {
		ge.logger.Error("goja_extractor", "JavaScript结果序列化失败", map[string]interface{}{
			"source": source,
			"error":  err.Error(),
		})
		return nil
	}
	
	// 解析JavaScript返回的结果
	var jsExtractData map[string]interface{}
	if err := json.Unmarshal(jsonStr, &jsExtractData); err != nil {
		ge.logger.Error("goja_extractor", "JavaScript结果解析失败", map[string]interface{}{
			"source": source,
			"error":  err.Error(),
		})
		return nil
	}
	
	// 创建SensitiveInfo结构体
	info := &SensitiveInfo{}
	
	// 转换各类敏感信息
	info.SFZ = ge.convertArrayToInfoItems(jsExtractData["sfz"], source)
	info.Phone = ge.convertArrayToInfoItems(jsExtractData["mobile"], source) // 注意：JS中是mobile，Go中是Phone
	info.Mail = ge.convertArrayToInfoItems(jsExtractData["mail"], source)
	info.IP = ge.convertArrayToInfoItems(jsExtractData["ip"], source)
	info.IPPort = ge.convertArrayToInfoItems(jsExtractData["ip_port"], source)
	info.Domain = ge.convertArrayToInfoItems(jsExtractData["domain"], source)
	info.URL = ge.convertArrayToInfoItems(jsExtractData["url"], source)
	info.JWT = ge.convertArrayToInfoItems(jsExtractData["jwt"], source)
	info.Algorithm = ge.convertArrayToInfoItems(jsExtractData["algorithm"], source)
	info.Secret = ge.convertArrayToInfoItems(jsExtractData["secret"], source)
	
	// 合并路径信息（JavaScript中分为path和incomplete_path）
	completePaths := ge.convertArrayToInfoItems(jsExtractData["path"], source)
	incompletePaths := ge.convertArrayToInfoItems(jsExtractData["incomplete_path"], source)
	info.Path = ge.mergePaths(completePaths, incompletePaths)
	
	return info
}

// convertArrayToInfoItems 将JavaScript数组转换为InfoItem切片
func (ge *GojaExtractor) convertArrayToInfoItems(jsArray interface{}, source string) []InfoItem {
	if jsArray == nil {
		return nil
	}

	// 尝试转换为字符串数组
	switch arr := jsArray.(type) {
	case []interface{}:
		var items []InfoItem
		for _, item := range arr {
			if str, ok := item.(string); ok && str != "" {
				// 清理值（移除引号等）
				cleanValue := ge.cleanValue(str)
				if cleanValue != "" && !ge.isDuplicateInItems(items, cleanValue) {
					items = append(items, InfoItem{
						Value:  cleanValue,
						Source: source,
					})
				}
			}
		}
		return items
	default:
		return nil
	}
}

// mergePaths 合并完整路径和不完整路径
func (ge *GojaExtractor) mergePaths(completePaths, incompletePaths []InfoItem) []InfoItem {
	// 创建一个映射来去重
	pathMap := make(map[string]InfoItem)

	// 添加完整路径
	for _, item := range completePaths {
		pathMap[item.Value] = item
	}

	// 添加不完整路径（如果不重复）
	for _, item := range incompletePaths {
		if _, exists := pathMap[item.Value]; !exists {
			pathMap[item.Value] = item
		}
	}

	// 转换回切片
	var result []InfoItem
	for _, item := range pathMap {
		result = append(result, item)
	}

	return result
}

// cleanValue 清理提取的值，移除引号和多余的字符
func (ge *GojaExtractor) cleanValue(value string) string {
	if len(value) == 0 {
		return ""
	}

	// 移除首尾的引号
	if (value[0] == '"' || value[0] == '\'') && len(value) > 1 {
		if value[len(value)-1] == value[0] {
			value = value[1 : len(value)-1]
		}
	}

	// 移除多余的空白字符
	value = strings.TrimSpace(value)

	return value
}

// isDuplicateInItems 检查值是否在InfoItem切片中重复
func (ge *GojaExtractor) isDuplicateInItems(items []InfoItem, value string) bool {
	for _, item := range items {
		if item.Value == value {
			return true
		}
	}
	return false
}

// Close 关闭goja提取器，清理资源
func (ge *GojaExtractor) Close() {
	ge.mu.Lock()
	defer ge.mu.Unlock()

	if ge.vm != nil {
		// goja运行时会自动进行垃圾回收，无需手动清理
		ge.vm = nil
		ge.extractFunc = nil
		ge.initialized = false

		ge.logger.Debug("goja_extractor", "goja提取器资源已清理", nil)
	}
}
