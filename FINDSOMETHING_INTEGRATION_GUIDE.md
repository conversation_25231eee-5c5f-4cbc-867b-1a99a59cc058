# FindSomething功能移植详细指南

## 📖 文档目标
本文档详细讲解如何将FindSomething浏览器扩展的敏感信息检测功能移植到WebScan爬虫工具中，适合有基础编程知识的开发者阅读。

## 🎯 移植目标
将FindSomething的JavaScript敏感信息检测逻辑完整移植到Go语言的WebScan项目中，实现：
- 100%兼容FindSomething的检测规则
- 支持所有敏感信息类型检测
- 保持原有的准确性和覆盖率

## 🔍 什么是FindSomething？

FindSomething是一个浏览器扩展，专门用于从网页的JavaScript代码中提取敏感信息，如：
- 身份证号码、手机号、邮箱
- API密钥、JWT令牌
- IP地址、域名、URL
- 加密算法调用等

**核心特点**：使用正则表达式匹配JavaScript源码中的敏感信息。

## 🏗️ 移植架构设计

### 原始架构（FindSomething）
```
浏览器页面 → content.js → background.js → extract_info() → 正则匹配 → 结果显示
```

### 移植后架构（WebScan）
```
网页内容 → GojaExtractor → JavaScript引擎 → extract_info() → Go结构体 → 数据库存储
```

## 🔧 移植实现步骤

### 第一步：理解FindSomething的核心代码

#### 1.1 核心文件分析
FindSomething项目的关键文件：
- `background.js` - 包含主要的检测逻辑
- `content.js` - 负责页面内容提取
- `nuclei_regex` - 710条密钥检测规则

#### 1.2 关键函数识别
```javascript
// 主要检测函数
function extract_info(data) {
    // 使用正则表达式提取各种敏感信息
}

// 密钥检测函数
function get_secret(data) {
    // 使用nuclei规则检测API密钥
}

// 辅助函数
function add(arr1, arr2) { /* 数组合并去重 */ }
function unique(arr1) { /* 数组去重 */ }
```

### 第二步：选择移植方案

我们有两种移植方案：

#### 方案A：纯Go实现（已废弃）
- 优点：性能好，纯Go代码
- 缺点：需要手动转换所有正则表达式，容易出错

#### 方案B：JavaScript引擎方案（已采用）
- 优点：100%兼容，直接运行原版JavaScript代码
- 缺点：需要JavaScript引擎，性能略低

**选择方案B的原因**：确保100%兼容性，避免手动转换错误。

### 第三步：集成JavaScript引擎

#### 3.1 选择goja引擎
```go
import "github.com/dop251/goja"
```

**为什么选择goja？**
- 纯Go实现的JavaScript引擎
- 支持ES5标准
- 性能良好，内存占用合理
- 社区活跃，文档完善

#### 3.2 创建GojaExtractor结构体
```go
type GojaExtractor struct {
    logger       *core.Logger
    errorManager *core.ErrorManager
    
    // JavaScript运行时和相关资源
    vm           *goja.Runtime
    extractFunc  goja.Callable
    mu           sync.Mutex // 并发安全保护
    
    initialized  bool
}
```

**设计要点**：
- 使用单例模式，避免重复初始化
- 互斥锁保护JavaScript运行时的并发访问
- 预编译JavaScript函数，提高性能

### 第四步：移植JavaScript代码

#### 4.1 完整复制原版代码
```go
func (ge *GojaExtractor) getFindSomethingJSCode() string {
    return `
    // 完全复制FindSomething的JavaScript代码
    var nuclei_regex = [
        // 710条nuclei正则表达式规则
    ];
    
    function extract_info(data) {
        // 完全复制原版extract_info函数
    }
    
    function get_secret(data) {
        // 完全复制原版get_secret函数
    }
    
    // 其他辅助函数...
    `
}
```

**关键原则**：
- 不修改任何JavaScript逻辑
- 保持所有正则表达式不变
- 包含所有辅助函数

#### 4.2 初始化JavaScript环境
```go
func (ge *GojaExtractor) initializeJSRuntime() error {
    // 创建JavaScript运行时
    ge.vm = goja.New()
    
    // 加载JavaScript代码
    _, err := ge.vm.RunString(jsCode)
    
    // 获取extract_info函数
    extractInfoValue := ge.vm.Get("extract_info")
    ge.extractFunc, ok = goja.AssertFunction(extractInfoValue)
    
    return nil
}
```

### 第五步：数据类型转换

#### 5.1 定义Go数据结构
```go
type SensitiveInfo struct {
    SFZ       []InfoItem `json:"sfz,omitempty"`       // 身份证
    Phone     []InfoItem `json:"phone,omitempty"`     // 手机号
    Mail      []InfoItem `json:"mail,omitempty"`      // 邮箱
    IP        []InfoItem `json:"ip,omitempty"`        // IP地址
    // ... 其他字段
}

type InfoItem struct {
    Value  string `json:"value"`  // 敏感信息值
    Source string `json:"source"` // 来源
}
```

#### 5.2 JavaScript到Go的数据转换
```go
func (ge *GojaExtractor) convertJSResultToGoStruct(jsResult goja.Value, source string) *SensitiveInfo {
    // 1. 将JavaScript对象转换为JSON
    jsonStr, err := json.Marshal(jsResult.Export())
    
    // 2. 解析JSON到Go map
    var jsExtractData map[string]interface{}
    json.Unmarshal(jsonStr, &jsExtractData)
    
    // 3. 转换为Go结构体
    info := &SensitiveInfo{}
    info.SFZ = ge.convertArrayToInfoItems(jsExtractData["sfz"], source)
    info.Phone = ge.convertArrayToInfoItems(jsExtractData["mobile"], source)
    // ... 其他字段转换
    
    return info
}
```

**转换要点**：
- JavaScript的`mobile`字段对应Go的`Phone`字段
- 数组类型需要逐个转换为InfoItem
- 保持数据清理逻辑一致

### 第六步：数据库集成

#### 6.1 设计数据库表结构
```sql
CREATE TABLE js_sensitive_info (
    id INT PRIMARY KEY AUTO_INCREMENT,
    task_id INT NOT NULL,
    info_type VARCHAR(50) NOT NULL,  -- sfz, mobile, mail等
    value TEXT NOT NULL,             -- 敏感信息值
    source TEXT NOT NULL,            -- 来源URL
    UNIQUE KEY (task_id, info_type, value, source)
);
```

#### 6.2 数据存储逻辑
```go
func (s *DBStorage) collectSensitiveInfo(taskID int64, result ScanResult, collector *ResourceCollector) {
    // 按照FindSomething的key数组存储
    s.addSensitiveItems(taskID, result.SensitiveInfo.SFZ, "sfz", collector)
    s.addSensitiveItems(taskID, result.SensitiveInfo.Phone, "mobile", collector) // 注意：使用mobile
    s.addSensitiveItems(taskID, result.SensitiveInfo.Mail, "mail", collector)
    // ... 其他类型
}
```

**存储要点**：
- `info_type`字段使用FindSomething的原始字段名
- 实现严格的去重逻辑
- 支持批量插入提高性能

## 🔄 关键技术难点及解决方案

### 难点1：JavaScript与Go的数据类型差异
**问题**：JavaScript的动态类型与Go的静态类型不匹配
**解决**：使用interface{}接收，然后类型断言转换

### 难点2：并发安全问题
**问题**：goja运行时不是线程安全的
**解决**：使用互斥锁保护JavaScript运行时访问

### 难点3：内存管理
**问题**：频繁创建JavaScript运行时消耗内存
**解决**：使用单例模式，复用JavaScript运行时

### 难点4：错误处理
**问题**：JavaScript执行可能出错
**解决**：完善的错误捕获和日志记录

## 📊 性能优化策略

### 1. 单例模式
```go
var (
    globalSensitiveInfoExtractor *GojaExtractor
    extractorOnce                sync.Once
)

func GetSensitiveInfoExtractor() *GojaExtractor {
    extractorOnce.Do(func() {
        globalSensitiveInfoExtractor = NewGojaExtractor()
    })
    return globalSensitiveInfoExtractor
}
```

### 2. 预编译JavaScript函数
- 初始化时编译一次
- 运行时直接调用，避免重复编译

### 3. 数据库批量操作
- 使用事务批量插入
- 实现连接池管理

## 🧪 测试验证方法

### 1. 单元测试
```go
func TestGojaExtractor() {
    extractor := NewGojaExtractor()
    testData := `var config = {"api_key": "sk-123", "email": "<EMAIL>"};`
    result := extractor.ExtractSensitiveInfo(testData, "test")
    
    // 验证提取结果
    assert.Equal(t, 1, len(result.Mail))
    assert.Equal(t, "<EMAIL>", result.Mail[0].Value)
}
```

### 2. 对比测试
- 使用相同数据分别在FindSomething和WebScan中测试
- 对比提取结果的一致性

### 3. 性能测试
- 测试大量数据的处理性能
- 验证内存使用情况

## 🎯 移植成果

### 兼容性达成
- ✅ 100%兼容FindSomething的检测规则
- ✅ 支持所有11种敏感信息类型
- ✅ 包含完整的710条nuclei规则

### 功能增强
- 🚀 数据库持久化存储
- 🚀 批量处理能力
- 🚀 项目隔离功能
- 🚀 详细的日志记录

## 📝 总结

通过使用JavaScript引擎的方案，我们成功将FindSomething的功能完整移植到WebScan中，实现了：

1. **100%兼容性** - 直接运行原版JavaScript代码
2. **高性能** - 单例模式和预编译优化
3. **企业级功能** - 数据库存储和批量处理
4. **易维护性** - 清晰的代码结构和完善的文档

这种移植方案既保证了功能的完整性，又提供了良好的扩展性，是一个成功的技术移植案例。

## 🛠️ 实际操作示例

### 示例1：提取敏感信息
```go
// 创建提取器实例
extractor := GetSensitiveInfoExtractor()

// 待分析的JavaScript代码
jsCode := `
var config = {
    "api_key": "sk-1234567890abcdef",
    "database_url": "mysql://user:password@localhost:3306/db",
    "email": "<EMAIL>",
    "phone": "13812345678"
};
`

// 提取敏感信息
result := extractor.ExtractSensitiveInfo(jsCode, "config.js")

// 输出结果
fmt.Printf("找到 %d 个邮箱地址\n", len(result.Mail))
fmt.Printf("找到 %d 个手机号码\n", len(result.Phone))
```

### 示例2：数据库存储
```go
// 存储到数据库
dbStorage := NewDBStorage()
taskID := int64(1)

// 创建扫描结果
scanResult := ScanResult{
    URL: "https://example.com",
    SensitiveInfo: result,
}

// 批量存储
err := dbStorage.BatchStoreResults(taskID, []ScanResult{scanResult})
if err != nil {
    log.Printf("存储失败: %v", err)
}
```

## 🔍 调试技巧

### 1. 启用详细日志
```go
// 在配置中启用DEBUG级别日志
logger := core.NewLogger()
logger.SetLevel("DEBUG")
```

### 2. JavaScript执行调试
```go
// 在JavaScript代码中添加console.log
jsCode := `
function extract_info(data) {
    console.log("开始处理数据:", data.length);
    // ... 处理逻辑
}
`
```

### 3. 性能监控
```go
start := time.Now()
result := extractor.ExtractSensitiveInfo(data, source)
duration := time.Since(start)
log.Printf("提取耗时: %v", duration)
```

## 🚨 常见问题及解决方案

### Q1: JavaScript执行失败
**现象**: 提取器返回nil或空结果
**原因**: JavaScript代码语法错误或运行时异常
**解决**:
```go
// 检查JavaScript代码是否正确加载
if !extractor.initialized {
    log.Error("提取器未正确初始化")
}
```

### Q2: 内存占用过高
**现象**: 长时间运行后内存持续增长
**原因**: JavaScript运行时未正确释放
**解决**: 使用单例模式，避免重复创建运行时

### Q3: 并发访问错误
**现象**: 多线程环境下出现panic
**原因**: goja运行时不是线程安全的
**解决**: 已使用互斥锁保护，确保同时只有一个goroutine访问

### Q4: 提取结果不准确
**现象**: 某些敏感信息未被检测到
**原因**: 正则表达式或JavaScript逻辑问题
**解决**: 对比原版FindSomething的结果，确保JavaScript代码完全一致

## 📚 扩展阅读

### 相关技术文档
- [goja JavaScript引擎文档](https://github.com/dop251/goja)
- [FindSomething项目地址](https://github.com/ResidualLaugh/FindSomething)
- [nuclei规则库](https://github.com/projectdiscovery/nuclei-templates)

### 进阶优化方向
1. **规则热更新**: 支持动态更新检测规则
2. **分布式处理**: 支持多节点并行处理
3. **机器学习**: 结合AI提高检测准确率
4. **实时监控**: 添加性能监控和告警

## 📄 附录：完整代码结构

```
WebScan项目结构:
├── internal/
│   ├── goja_extractor.go      # JavaScript引擎提取器
│   ├── findsomething_extractor.go  # 原Go实现（已废弃）
│   ├── database.go            # 数据库操作
│   ├── scan.go               # 扫描主逻辑
│   └── type.go               # 数据结构定义
├── FindSomething/            # 原版FindSomething项目
│   ├── background.js         # 核心检测逻辑
│   └── content.js           # 页面内容提取
├── config/
│   └── database.json        # 数据库配置
└── cmd/
    └── main.go              # 程序入口
```

通过这份详细指南，开发者可以清楚地了解整个移植过程，并能够根据需要进行类似的技术移植工作。
